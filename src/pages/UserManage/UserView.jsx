import React from "react";

function UserView({ viewUser, isViewModalOpen, handleCloseViewModal }) {
  if (!viewUser) return null;

  return (
    <div className="fixed inset-0 flex items-center justify-center z-[99999999999] bg-black bg-opacity-40">
      <div className="bg-white dark:bg-gray-800 rounded-lg p-6 w-full max-w-lg shadow-lg h-auto">
        <h2 className="text-xl font-bold mb-4 text-gray-800 dark:text-white text-center relative">
          User Details
          <span
            className="bg-blue-600 p-1 border rounded-[5px] absolute -top-5 -right-5 cursor-pointer text-white"
            onClick={handleCloseViewModal}
          >
            X
          </span>
        </h2>
        <div className="grid grid-cols-2 gap-4 text-gray-700 dark:text-gray-200">
          {/* Profile Picture */}
          {viewUser.profile_picture && (
            <img
              src={`https://room8.flexioninfotech.com${viewUser.profile_picture}`}
              alt="Profile"
              className="w-24 h-24 rounded-full object-cover mb-4 border-2 border-gray-300 dark:border-gray-600 ms-5"
            />
          )}

          <p className="flex items-center mb-4">
            <strong>Full Name:</strong> {viewUser.full_name}
          </p>
          <p>
            <strong>About:</strong> {viewUser.about}
          </p>
          <p>
            <strong>Email:</strong> {viewUser.email || "N/A"}
          </p>
          <p>
            <strong>Contact:</strong> {viewUser.contact_number}
          </p>
          <p>
            <strong>Date of Birth:</strong> {viewUser.dob}
          </p>
          <p>
            <strong>Gender:</strong> {viewUser.gender}
          </p>
          <p>
            <strong>Preferred Gender:</strong> {viewUser.prefered_gender}
          </p>
          <p>
            <strong>Class Standing:</strong> {viewUser.class_standing}
          </p>
          <p>
            <strong>Cleanliness:</strong> {viewUser.cleaniness}
          </p>
          <p>
            <strong>Lease Period:</strong> {viewUser.prefered_lease_period} days
          </p>
          <p>
            <strong>Smoking Preference:</strong> {viewUser.prefered_smoking}
          </p>
          <p>
            <strong>Has Pet:</strong> {viewUser.is_having_pet ? "Yes" : "No"}
          </p>
          <p>
            <strong>Verified:</strong> {viewUser.is_verified ? "Yes" : "No"}
          </p>
          <p>
            <strong>Active:</strong> {viewUser.is_active ? "Yes" : "No"}
          </p>

          <p>
            <strong>Personality:</strong>{" "}
            {Array.isArray(viewUser.personality_type_description)
              ? viewUser.personality_type_description.join(", ")
              : viewUser.personality_type_description}
          </p>

          <p>
            <strong>Hobbies:</strong>{" "}
            {Array.isArray(viewUser.interests_hobbies)
              ? viewUser.interests_hobbies.join(", ")
              : viewUser.interests_hobbies}
          </p>

          <p>
            <strong>Lifestyle:</strong>{" "}
            {Array.isArray(viewUser.living_style)
              ? viewUser.living_style.join(", ")
              : viewUser.living_style}
          </p>

          <p>
            <strong>Habits:</strong>{" "}
            {Array.isArray(viewUser.habits_lifestyle)
              ? viewUser.habits_lifestyle.join(", ")
              : viewUser.habits_lifestyle}
          </p>

          <p>
            <strong>Preferred Locations:</strong> {viewUser.prefered_locations}
          </p>
        </div>

        {/* <button
          onClick={handleCloseViewModal}
          className="mt-6 w-full py-2 bg-blue-600 hover:bg-blue-700 text-white rounded"
        >
          Close
        </button> */}
      </div>
    </div>
  );
}

export default UserView;
