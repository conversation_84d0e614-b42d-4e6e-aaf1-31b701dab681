import React, { useEffect, useState, useRef } from "react";

// Mock data to simulate API response
const mockUserData = {
  results: {
    data: [
      {
        id: 1,
        username: "john_doe",
        email: "<EMAIL>",
        profile_image: null,
        date: "2024-01-15T10:30:00Z",
        connected_social_platforms: "Facebook, Instagram",
        posts: 25,
        is_deleted: false
      },
      {
        id: 2,
        username: "jane_smith",
        email: "<EMAIL>",
        profile_image: null,
        date: "2024-01-20T14:45:00Z",
        connected_social_platforms: "Twitter, LinkedIn",
        posts: 18,
        is_deleted: false
      },
      {
        id: 3,
        username: "mike_wilson",
        email: "<EMAIL>",
        profile_image: null,
        date: "2024-01-25T09:15:00Z",
        connected_social_platforms: "Instagram",
        posts: 12,
        is_deleted: true
      },
      {
        id: 4,
        username: "sarah_jones",
        email: "<EMAIL>",
        profile_image: null,
        date: "2024-02-01T16:20:00Z",
        connected_social_platforms: "Facebook, Twitter, Instagram",
        posts: 34,
        is_deleted: false
      },
      {
        id: 5,
        username: "alex_brown",
        email: "<EMAIL>",
        profile_image: null,
        date: "2024-02-05T11:30:00Z",
        connected_social_platforms: "LinkedIn",
        posts: 8,
        is_deleted: false
      }
    ],
    additional_data: {
      users_in_last_24h: 12,
      users_in_last_7d: 45,
      users_in_last_30d: 156,
      users_in_last_90d: 342,
      users_in_last_180d: 567,
      users_in_last_365d: 892
    }
  },
  count: 5
};

// Simple Pagination Component
const Pagination = ({ totalPages, currentPage, handlePrevious, handleNext, handlePageClick }) => {
  const pages = [];
  for (let i = 1; i <= totalPages; i++) {
    pages.push(i);
  }

  return (
    <div className="flex justify-center items-center space-x-2 mt-8">
      <button
        onClick={handlePrevious}
        disabled={currentPage === 1}
        className="px-4 py-2 bg-red-500 text-white rounded-lg disabled:bg-gray-300 dark:disabled:bg-gray-600 disabled:cursor-not-allowed hover:bg-red-600 transition-colors"
      >
        Previous
      </button>
      
      {pages.map(page => (
        <button
          key={page}
          onClick={() => handlePageClick(page)}
          className={`px-4 py-2 rounded-lg transition-colors ${
            currentPage === page
              ? 'bg-blue-500 text-white'
              : 'bg-gray-200 dark:bg-gray-700 text-gray-700 dark:text-gray-300 hover:bg-gray-300 dark:hover:bg-gray-600'
          }`}
        >
          {page}
        </button>
      ))}
      
      <button
        onClick={handleNext}
        disabled={currentPage === totalPages}
        className="px-4 py-2 bg-red-500 text-white rounded-lg disabled:bg-gray-300 dark:disabled:bg-gray-600 disabled:cursor-not-allowed hover:bg-red-600 transition-colors"
      >
        Next
      </button>
    </div>
  );
};

// Delete Confirmation Modal
const DeleteModal = ({ isOpen, onClose, user, onConfirm }) => {
  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 flex items-center justify-center z-50 bg-black bg-opacity-50 dark:bg-opacity-70">
      <div className="bg-white dark:bg-gray-800 rounded-lg p-6 max-w-md w-full mx-4 shadow-xl">
        <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">Confirm Delete</h3>
        <p className="text-gray-600 dark:text-gray-300 mb-6">
          Are you sure you want to delete user "{user?.username}"? This action cannot be undone.
        </p>
        <div className="flex justify-end space-x-4">
          <button
            onClick={onClose}
            className="px-4 py-2 text-gray-600 dark:text-gray-300 border border-gray-300 dark:border-gray-600 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors"
          >
            Cancel
          </button>
          <button
            onClick={onConfirm}
            className="px-4 py-2 bg-red-500 text-white rounded-lg hover:bg-red-600 transition-colors"
          >
            Delete
          </button>
        </div>
      </div>
    </div>
  );
};

// Loading Skeleton
const TableSkeleton = () => (
  <div className="animate-pulse">
    {[...Array(5)].map((_, i) => (
      <div key={i} className="bg-white dark:bg-gray-800 rounded-lg p-4 mb-4 shadow">
        <div className="grid grid-cols-10 gap-4">
          {[...Array(10)].map((_, j) => (
            <div key={j} className="h-4 bg-gray-200 dark:bg-gray-700 rounded"></div>
          ))}
        </div>
      </div>
    ))}
  </div>
);

const UserManagement = () => {
  const [currentPage, setCurrentPage] = useState(1);
  const itemsPerPage = 15;
  const [userlist, setUserlist] = useState([]);
  const [loading, setLoading] = useState(true);
  const [totalPages, setTotalPages] = useState(1);
  const [deleteOpen, setDeleteOpen] = useState(false);
  const [selectedUser, setSelectedUser] = useState(null);
  const [additionalData, setAdditionalData] = useState({});
  const [dateRange, setDateRange] = useState([null, null]);
  const [startDate, endDate] = dateRange;
  const containerRef = useRef(null);
  const cardsRef = useRef(null);

  // Mock API call
  const fetchUserManage = async (page, from = startDate, to = endDate) => {
    try {
      setLoading(true);
      // Simulate API delay
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      const users = mockUserData.results.data || [];
      setUserlist(users);
      setTotalPages(Math.ceil(mockUserData.count / itemsPerPage));
      setAdditionalData(mockUserData.results.additional_data || {});
    } catch (error) {
      console.error("Error fetching users:", error);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchUserManage(currentPage, startDate, endDate);
  }, [currentPage]);

  useEffect(() => {
    if (!loading && cardsRef.current) {
      cardsRef.current.scrollIntoView({ behavior: "smooth" });
    }
  }, [currentPage, loading]);

  const handlePrevious = () => {
    if (currentPage > 1) {
      setCurrentPage(currentPage - 1);
    }
  };

  const handleNext = () => {
    if (currentPage < totalPages) {
      setCurrentPage(currentPage + 1);
    }
  };

  const handlePageClick = (page) => {
    setCurrentPage(page);
  };

  const handleDialogClose = () => setDeleteOpen(false);

  const handleClickOpen = (user) => {
    if (!user.is_deleted) {
      setSelectedUser(user);
      setDeleteOpen(true);
    }
  };

  const handleConfirmDelete = () => {
    if (selectedUser) {
      // Update the user in the list to mark as deleted
      setUserlist(prevUsers => 
        prevUsers.map(user => 
          user.id === selectedUser.id 
            ? { ...user, is_deleted: true }
            : user
        )
      );
      setDeleteOpen(false);
      setSelectedUser(null);
    }
  };

  const handleDateRangeChange = (update) => {
    setDateRange(update);
  };

  const handleSearchSubmit = () => {
    setCurrentPage(1);
    fetchUserManage(1, startDate, endDate);
  };

  if (loading) {
    return (
      <div className="p-4 bg-gray-50 dark:bg-gray-900 min-h-screen">
        <div className="mt-20">
          <TableSkeleton />
        </div>
      </div>
    );
  }

  return (
    <div
      ref={containerRef}
      className="p-4 bg-gray-50 dark:bg-gray-900 min-h-screen transition-colors duration-300"
    >
      <div className="flex justify-between items-center">
        <h2 className="text-xl md:text-2xl lg:text-3xl mt-20 font-bold mb-0 pt-5 text-gray-900 dark:text-white">
          User Management
        </h2>
      </div>

      {/* Additional Data Cards */}
      <div
        ref={cardsRef}
        className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-6 gap-4 mt-6 mb-8"
      >
        <div className="bg-white dark:bg-gray-800 rounded-xl shadow p-5 flex flex-col items-center transition-colors duration-300">
          <span className="text-2xl font-bold text-gray-600 dark:text-gray-300">
            {additionalData.users_in_last_24h ?? 0}
          </span>
          <span className="text-gray-500 dark:text-gray-400 text-sm mt-2 text-center">
            Users in last 24h
          </span>
        </div>
        <div className="bg-white dark:bg-gray-800 rounded-xl shadow p-5 flex flex-col items-center transition-colors duration-300">
          <span className="text-2xl font-bold text-gray-600 dark:text-gray-300">
            {additionalData.users_in_last_7d ?? 0}
          </span>
          <span className="text-gray-500 dark:text-gray-400 text-sm mt-2 text-center">
            Users in last 7 days
          </span>
        </div>
        <div className="bg-white dark:bg-gray-800 rounded-xl shadow p-5 flex flex-col items-center transition-colors duration-300">
          <span className="text-2xl font-bold text-gray-600 dark:text-gray-300">
            {additionalData.users_in_last_30d ?? 0}
          </span>
          <span className="text-gray-500 dark:text-gray-400 text-sm mt-2 text-center">
            Users in last 30 days
          </span>
        </div>
        <div className="bg-white dark:bg-gray-800 rounded-xl shadow p-5 flex flex-col items-center transition-colors duration-300">
          <span className="text-2xl font-bold text-gray-600 dark:text-gray-300">
            {additionalData.users_in_last_90d ?? 0}
          </span>
          <span className="text-gray-500 dark:text-gray-400 text-sm mt-2 text-center">
            Users in last 90 days
          </span>
        </div>
        <div className="bg-white dark:bg-gray-800 rounded-xl shadow p-5 flex flex-col items-center transition-colors duration-300">
          <span className="text-2xl font-bold text-gray-600 dark:text-gray-300">
            {additionalData.users_in_last_180d ?? 0}
          </span>
          <span className="text-gray-500 dark:text-gray-400 text-sm mt-2 text-center">
            Users in last 180 days
          </span>
        </div>
        <div className="bg-white dark:bg-gray-800 rounded-xl shadow p-5 flex flex-col items-center transition-colors duration-300">
          <span className="text-2xl font-bold text-gray-600 dark:text-gray-300">
            {additionalData.users_in_last_365d ?? 0}
          </span>
          <span className="text-gray-500 dark:text-gray-400 text-sm mt-2 text-center">
            Users in last 365 days
          </span>
        </div>
      </div>

      {/* Search and Date Range Filter */}
      <div className="flex flex-col md:flex-row gap-4 items-center mb-6 mt-4">
        <div className="w-full md:w-auto bg-white dark:bg-gray-800 rounded-xl shadow p-4 flex flex-col md:flex-row items-center gap-4 border border-gray-100 dark:border-gray-700 transition-colors duration-300">
          <label className="block text-gray-700 dark:text-gray-300 font-semibold mb-2 md:mb-0 md:mr-4 text-sm md:text-base">
            Date Range Filter
          </label>
          <div className="flex gap-2 items-center">
            <input
              type="date"
              value={startDate || ''}
              onChange={(e) => handleDateRangeChange([e.target.value, endDate])}
              className="border border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white rounded-md px-4 py-2 focus:border-red-500 focus:ring-2 focus:ring-red-200 dark:focus:ring-red-900 transition-all duration-150 shadow-sm hover:border-red-500"
            />
            <span className="mx-1 text-gray-500 dark:text-gray-400 font-semibold">to</span>
            <input
              type="date"
              value={endDate || ''}
              onChange={(e) => handleDateRangeChange([startDate, e.target.value])}
              className="border border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white rounded-md px-4 py-2 focus:border-red-500 focus:ring-2 focus:ring-red-200 dark:focus:ring-red-900 transition-all duration-150 shadow-sm hover:border-red-500"
            />
          </div>
        </div>
        <button
          onClick={handleSearchSubmit}
          className="bg-blue-500 text-white px-6 py-2 rounded-md font-semibold hover:bg-blue-600 focus:ring-2 focus:ring-blue-300 dark:focus:ring-blue-700 transition-all duration-150 shadow-md"
        >
          Search
        </button>
      </div>

      {/* Users Table */}
      <div className="rounded-2xl border border-gray-200 dark:border-gray-700 bg-white dark:bg-gray-800 px-5 pt-6 pb-2.5 shadow-default sm:px-7.5 xl:pb-1 transition-colors duration-300">
        <div className="py-6">
          <div className="overflow-hidden rounded-lg shadow">
            <div className="overflow-x-auto">
              <table className="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
                <thead className="bg-gray-50 dark:bg-gray-700">
                  <tr>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">S. No.</th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">User ID</th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Profile</th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Username</th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Email</th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Date</th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Social Media</th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Posts</th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Status</th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Action</th>
                  </tr>
                </thead>
                <tbody className="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
                  {userlist.map((user, index) => (
                    <tr key={user.id} className="hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors duration-200">
                      <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-600 dark:text-gray-300">
                        {(currentPage - 1) * itemsPerPage + index + 1}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-600 dark:text-gray-300">
                        {user.id}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="flex items-center justify-center">
                          <div className="h-12 w-12 rounded-full bg-gray-200 dark:bg-gray-600 border-2 border-gray-300 dark:border-gray-500 text-gray-600 dark:text-gray-300 flex items-center justify-center">
                            <span className="text-red-500 dark:text-red-400 font-semibold">
                              {user.username.charAt(0).toUpperCase()}
                            </span>
                          </div>
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-600 dark:text-gray-300">
                        {user.username}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-600 dark:text-gray-300">
                        {user.email}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-600 dark:text-gray-300">
                        {new Date(user.date).toLocaleDateString()}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-600 dark:text-gray-300">
                        {user.connected_social_platforms}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-600 dark:text-gray-300">
                        {user.posts}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        {user.is_deleted ? (
                          <span className="inline-flex px-3 py-1 text-xs font-semibold rounded-full bg-red-100 dark:bg-red-900/30 text-red-800 dark:text-red-200">
                            Deleted
                          </span>
                        ) : (
                          <span className="inline-flex px-3 py-1 text-xs font-semibold rounded-full bg-green-100 dark:bg-green-900/30 text-green-800 dark:text-green-200">
                            Active
                          </span>
                        )}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                        {!user.is_deleted && (
                          <button
                            onClick={() => handleClickOpen(user)}
                            className="text-red-600 dark:text-red-400 hover:text-red-900 dark:hover:text-red-300 p-2 rounded-md hover:bg-red-50 dark:hover:bg-red-900/20 transition-colors"
                          >
                            <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path>
                            </svg>
                          </button>
                        )}
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          </div>

          <Pagination
            totalPages={totalPages}
            currentPage={currentPage}
            handlePrevious={handlePrevious}
            handleNext={handleNext}
            handlePageClick={handlePageClick}
          />
        </div>
      </div>

      <DeleteModal
        isOpen={deleteOpen}
        onClose={handleDialogClose}
        user={selectedUser}
        onConfirm={handleConfirmDelete}
      />
    </div>
  );
};

export default UserManagement;