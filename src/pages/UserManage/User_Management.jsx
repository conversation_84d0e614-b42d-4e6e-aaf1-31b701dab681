import React, { useEffect, useState, useRef } from "react";
import URL from "../../axios/URl";
import api from "../../axios/axiosInstance";
import UserView from "./UserView";

// Enhanced Pagination Component
const Pagination = ({
  totalPages,
  currentPage,
  handlePrevious,
  handleNext,
  handlePageClick,
  totalCount,
  itemsPerPage,
}) => {
  // Always show pagination info, but hide controls if only 1 page
  const showControls = totalPages > 1;

  const pages = [];
  const maxVisiblePages = 5;

  // Calculate page range to show
  let startPage = Math.max(1, currentPage - Math.floor(maxVisiblePages / 2));
  let endPage = Math.min(totalPages, startPage + maxVisiblePages - 1);

  // Adjust start page if we're near the end
  if (endPage - startPage + 1 < maxVisiblePages) {
    startPage = Math.max(1, endPage - maxVisiblePages + 1);
  }

  for (let i = startPage; i <= endPage; i++) {
    pages.push(i);
  }

  // Calculate showing range
  const startItem = (currentPage - 1) * itemsPerPage + 1;
  const endItem = Math.min(currentPage * itemsPerPage, totalCount);

  return (
    <div className="flex flex-col items-center space-y-4 mt-8">
      {/* Pagination Info */}
      <div className="text-sm text-gray-600 dark:text-gray-400">
        Showing {startItem} to {endItem} of {totalCount} results
      </div>

      {/* Pagination Controls */}
      {showControls && (
        <div className="flex justify-center items-center space-x-2">
          {/* Previous Button */}
          <button
            onClick={handlePrevious}
            disabled={currentPage === 1}
            className="px-4 py-2 bg-blue-500 text-white rounded-lg disabled:bg-gray-300 dark:disabled:bg-gray-600 disabled:cursor-not-allowed hover:bg-blue-600 transition-colors flex items-center space-x-1"
          >
            <svg
              className="w-4 h-4"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth="2"
                d="M15 19l-7-7 7-7"
              />
            </svg>
            <span>Previous</span>
          </button>

          {/* First page */}
          {startPage > 1 && (
            <>
              <button
                onClick={() => handlePageClick(1)}
                className="px-3 py-2 rounded-lg bg-gray-200 dark:bg-gray-700 text-gray-700 dark:text-gray-300 hover:bg-gray-300 dark:hover:bg-gray-600 transition-colors"
              >
                1
              </button>
              {startPage > 2 && <span className="px-2 text-gray-500">...</span>}
            </>
          )}

          {/* Page numbers */}
          {pages.map((page) => (
            <button
              key={page}
              onClick={() => handlePageClick(page)}
              className={`px-3 py-2 rounded-lg transition-colors ${
                currentPage === page
                  ? "bg-blue-500 text-white shadow-md"
                  : "bg-gray-200 dark:bg-gray-700 text-gray-700 dark:text-gray-300 hover:bg-gray-300 dark:hover:bg-gray-600"
              }`}
            >
              {page}
            </button>
          ))}

          {/* Last page */}
          {endPage < totalPages && (
            <>
              {endPage < totalPages - 1 && (
                <span className="px-2 text-gray-500">...</span>
              )}
              <button
                onClick={() => handlePageClick(totalPages)}
                className="px-3 py-2 rounded-lg bg-gray-200 dark:bg-gray-700 text-gray-700 dark:text-gray-300 hover:bg-gray-300 dark:hover:bg-gray-600 transition-colors"
              >
                {totalPages}
              </button>
            </>
          )}

          {/* Next Button */}
          <button
            onClick={handleNext}
            disabled={currentPage === totalPages}
            className="px-4 py-2 bg-blue-500 text-white rounded-lg disabled:bg-gray-300 dark:disabled:bg-gray-600 disabled:cursor-not-allowed hover:bg-blue-600 transition-colors flex items-center space-x-1"
          >
            <span>Next</span>
            <svg
              className="w-4 h-4"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth="2"
                d="M9 5l7 7-7 7"
              />
            </svg>
          </button>
        </div>
      )}
    </div>
  );
};

// Delete Confirmation Modal
const DeleteModal = ({ isOpen, onClose, user, onConfirm, loading }) => {
  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 flex items-center justify-center z-50 bg-black bg-opacity-50 dark:bg-opacity-70">
      <div className="bg-white dark:bg-gray-800 rounded-lg p-6 max-w-md w-full mx-4 shadow-xl">
        <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">
          Confirm Delete
        </h3>
        <p className="text-gray-600 dark:text-gray-300 mb-6">
          Are you sure you want to delete user "{user?.username}"? This action
          cannot be undone.
        </p>
        <div className="flex justify-end space-x-4">
          <button
            onClick={onClose}
            disabled={loading}
            className="px-4 py-2 text-gray-600 dark:text-gray-300 border border-gray-300 dark:border-gray-600 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors disabled:opacity-50"
          >
            Cancel
          </button>
          <button
            onClick={onConfirm}
            disabled={loading}
            className="px-4 py-2 bg-red-500 text-white rounded-lg hover:bg-red-600 transition-colors disabled:opacity-50 flex items-center"
          >
            {loading ? (
              <>
                <svg
                  className="animate-spin -ml-1 mr-3 h-4 w-4 text-white"
                  xmlns="http://www.w3.org/2000/svg"
                  fill="none"
                  viewBox="0 0 24 24"
                >
                  <circle
                    className="opacity-25"
                    cx="12"
                    cy="12"
                    r="10"
                    stroke="currentColor"
                    strokeWidth="4"
                  ></circle>
                  <path
                    className="opacity-75"
                    fill="currentColor"
                    d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
                  ></path>
                </svg>
                Deleting...
              </>
            ) : (
              "Delete"
            )}
          </button>
        </div>
      </div>
    </div>
  );
};

// Loading Skeleton
const TableSkeleton = () => (
  <div className="animate-pulse">
    {[...Array(5)].map((_, i) => (
      <div
        key={i}
        className="bg-white dark:bg-gray-800 rounded-lg p-4 mb-4 shadow"
      >
        <div className="grid grid-cols-10 gap-4">
          {[...Array(10)].map((_, j) => (
            <div
              key={j}
              className="h-4 bg-gray-200 dark:bg-gray-700 rounded"
            ></div>
          ))}
        </div>
      </div>
    ))}
  </div>
);

const UserManagement = () => {
  const [currentPage, setCurrentPage] = useState(1);
  const [itemsPerPage] = useState(15);
  const [userlist, setUserlist] = useState([]);

  const [dashboardData, setDashboardData] = useState({
    total_users: 0,
    total_male: 0,
    total_female: 0,
  });
  const [loading, setLoading] = useState(true);
  const [deleteLoading, setDeleteLoading] = useState(false);
  const [totalPages, setTotalPages] = useState(1);
  const [totalCount, setTotalCount] = useState(10);
  const [deleteOpen, setDeleteOpen] = useState(false);
  const [selectedUser, setSelectedUser] = useState(null);
  const [dateRange, setDateRange] = useState([null, null]);
  const [startDate, endDate] = dateRange;
  const containerRef = useRef(null);
  const cardsRef = useRef(null);
  const [viewUser, setViewUser] = useState(null);
  const [vloading, setVLoading] = useState(false);
  const [viewLoadingId, setViewLoadingId] = useState(null);

  const [isViewModalOpen, setIsViewModalOpen] = useState(false);

  // Fetch dashboard data
  const fetchDashboardData = async () => {
    try {
      const response = await api.get(URL.GET_ALL_USER);
      const dashdata =
        response?.data?.results?.dashboard ||
        response?.data?.results?.deshboard;
      if (dashdata) {
        setDashboardData({
          total_users: dashdata.total_users || 0,
          total_male: dashdata.total_male || 0,
          total_female: dashdata.total_female || 0,
        });
      }
    } catch (error) {
      console.error("Error fetching dashboard data:", error);
    }
  };

  // Fetch users with pagination and filters
  const fetchUsers = async (page = 1, startDate = null, endDate = null) => {
    try {
      setLoading(true);

      // Build query parameters
      const params = new URLSearchParams({
        page: page.toString(),
        limit: itemsPerPage.toString(),
      });

      if (startDate) {
        params.append("start_date", startDate);
      }
      if (endDate) {
        params.append("end_date", endDate);
      }

      const response = await api.get(
        `${URL.GET_ALL_USER}?${params.toString()}`
      );
      const data = response?.data?.results;

      if (data) {
        setUserlist(data.data || []);
        setTotalCount(data.count || 0);
        setTotalPages(Math.ceil((data.count || 0) / itemsPerPage));

        // Update dashboard data if available
        const dashdata = data.dashboard || data.deshboard;
        if (dashdata) {
          setDashboardData({
            total_users: dashdata.total_users || 0,
            total_male: dashdata.total_male || 0,
            total_female: dashdata.total_female || 0,
          });
        }
      }
    } catch (error) {
      console.error("Error fetching users:", error);
      // Handle error - maybe show a toast notification
      setUserlist([]);
      setTotalCount(0);
      setTotalPages(1);
    } finally {
      setLoading(false);
    }
  };

  // Delete user
  const deleteUser = async (userId) => {
    try {
      setDeleteLoading(true);
      await api.post(`${URL.DELETE_USER}?user_id=${userId}`);

      await fetchUsers(currentPage, startDate, endDate);
      if (userlist.length === 1 && currentPage > 1) {
        setCurrentPage(currentPage - 1);
        await fetchUsers(currentPage - 1, startDate, endDate);
      }

      // Refresh dashboard data
      await fetchDashboardData();
    } catch (error) {
      console.error("Error deleting user:", error);
      // Handle error - maybe show a toast notification
    } finally {
      setDeleteLoading(false);
    }
  };

  // Initial load
  useEffect(() => {
    fetchUsers(1);
    fetchDashboardData();
  }, []);

  useEffect(() => {
    if (!loading && cardsRef.current) {
      cardsRef.current.scrollIntoView({ behavior: "smooth" });
    }
  }, [currentPage, loading]);

  const handlePrevious = () => {
    if (currentPage > 1) {
      const newPage = currentPage - 1;
      setCurrentPage(newPage);
      fetchUsers(newPage, startDate, endDate);
    }
  };

  const handleNext = () => {
    if (currentPage < totalPages) {
      const newPage = currentPage + 1;
      setCurrentPage(newPage);
      fetchUsers(newPage, startDate, endDate);
    }
  };

  const handlePageClick = (page) => {
    setCurrentPage(page);
    fetchUsers(page, startDate, endDate);
  };

  const handleDialogClose = () => {
    if (!deleteLoading) {
      setDeleteOpen(false);
      setSelectedUser(null);
    }
  };

  const handleClickOpen = (user) => {
    if (!user.is_deleted) {
      setSelectedUser(user);
      setDeleteOpen(true);
    }
  };

  const handleConfirmDelete = async () => {
    if (selectedUser) {
      await deleteUser(selectedUser.id);
      setDeleteOpen(false);
      setSelectedUser(null);
    }
  };

  const handleDateRangeChange = (update) => {
    setDateRange(update);
  };

  const handleSearchSubmit = () => {
    setCurrentPage(1);
    fetchUsers(1, startDate, endDate);
  };

  const fetchSingleUser = async (userId) => {
    setVLoading(true);
    try {
      const response = await api.get(`${URL.GET_ALL_USER}?user_id=${userId}`);
      return response.data;
    } catch (error) {
      console.error("Error fetching user details:", error);
    } finally {
      setViewLoadingId(null);
      setVLoading(false);
    }
  };

  const handleView = async (user) => {
    setViewLoadingId(user.id);
    const userDetails = await fetchSingleUser(user.id);
    if (userDetails) {
      setViewUser(userDetails.data);
      setIsViewModalOpen(true);
    }
  };

  const handleCloseViewModal = () => {
    setIsViewModalOpen(false);
    setViewUser(null);
  };

  const handleClearFilters = () => {
    setDateRange([null, null]);
    setCurrentPage(1);
    fetchUsers(1, null, null);
  };

  if (loading && currentPage === 1) {
    return (
      <div className="p-4 bg-gray-50 dark:bg-gray-900 min-h-screen">
        <div className="mt-20">
          <TableSkeleton />
        </div>
      </div>
    );
  }

  return (
    <div
      // ref={(el) => {
      //   containerRef.current = el;
      //   cardsRef.current = el;
      // }}
      className="p-4 bg-gray-50 dark:bg-gray-900 min-h-screen "
    >
      <div className="flex justify-between items-center">
        <h2 className="text-xl md:text-2xl lg:text-3xl  font-bold mb-0 pt-5 text-gray-900 dark:text-white">
          User Management
        </h2>
      </div>

      {/* Dashboard Cards */}
      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-3 gap-6 mt-6 mb-8">
        {/* Total Users */}
        <div className="bg-gradient-to-br from-blue-100 to-blue-200 shadow hover:scale-102 transition-transform duration-300 rounded-2xl p-5 flex flex-col gap-2">
          <div className="flex items-center justify-between">
            <h2 className="font-bold text-gray-700 text-lg">Total Users</h2>
            <div className="bg-blue-500 text-white p-2 rounded-full">
              <svg
                className="w-5 h-5"
                fill="none"
                stroke="currentColor"
                strokeWidth="2"
                viewBox="0 0 24 24"
              >
                <path d="M17 20h5v-2a4 4 0 00-3-3.87M9 20H4v-2a4 4 0 013-3.87m9-4a4 4 0 11-8 0 4 4 0 018 0z" />
              </svg>
            </div>
          </div>
          <p className="text-3xl font-extrabold text-blue-800">
            {dashboardData.total_users.toLocaleString()}
          </p>
        </div>

        {/* Male */}
        <div className="bg-gradient-to-br from-green-100 to-green-200 shadow hover:scale-102 transition-transform duration-300 rounded-2xl p-5 flex flex-col gap-2">
          <div className="flex items-center justify-between">
            <h2 className="font-bold text-gray-700 text-lg">Male</h2>
            <div className="bg-green-500 text-white p-2 rounded-full">
              <svg
                className="w-5 h-5"
                fill="none"
                stroke="currentColor"
                strokeWidth="2"
                viewBox="0 0 24 24"
              >
                <path d="M12 4v16m8-8H4" />
              </svg>
            </div>
          </div>
          <p className="text-3xl font-extrabold text-green-800">
            {dashboardData.total_male.toLocaleString()}
          </p>
        </div>

        <div className="bg-gradient-to-br from-pink-100 to-pink-200 shadow hover:scale-102 transition-transform duration-300 rounded-2xl p-5 flex flex-col gap-2">
          <div className="flex items-center justify-between">
            <h2 className="font-bold text-gray-700 text-lg">Female</h2>
            <div className="bg-pink-500 text-white p-2 rounded-full">
              <svg
                className="w-5 h-5"
                fill="none"
                stroke="currentColor"
                strokeWidth="2"
                viewBox="0 0 24 24"
              >
                <path d="M12 14v7m0 0h-3m3 0h3M16.24 7a4 4 0 11-8.48 0 4 4 0 018.48 0z" />
              </svg>
            </div>
          </div>
          <p className="text-3xl font-extrabold text-pink-800">
            {dashboardData.total_female.toLocaleString()}
          </p>
        </div>
      </div>

      {/* Search and Date Range Filter */}
      <div className="flex flex-col md:flex-row gap-4 items-center mb-6 mt-4">
        <div className="w-full md:w-auto bg-white dark:bg-gray-800 rounded-xl shadow p-4 flex flex-col md:flex-row items-center gap-4 border border-gray-100 dark:border-gray-700 transition-colors duration-300">
          <label className="block text-gray-700 dark:text-gray-300 font-semibold mb-2 md:mb-0 md:mr-4 text-sm md:text-base">
            Date Range Filter
          </label>
          <div className="flex gap-2 items-center">
            <input
              type="date"
              value={startDate || ""}
              onChange={(e) => handleDateRangeChange([e.target.value, endDate])}
              className="border border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white rounded-md px-4 py-2 focus:border-red-500 focus:ring-2 focus:ring-red-200 dark:focus:ring-red-900 transition-all duration-150 shadow-sm hover:border-red-500"
            />
            <span className="mx-1 text-gray-500 dark:text-gray-400 font-semibold">
              to
            </span>
            <input
              type="date"
              value={endDate || ""}
              onChange={(e) =>
                handleDateRangeChange([startDate, e.target.value])
              }
              className="border border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white rounded-md px-4 py-2 focus:border-red-500 focus:ring-2 focus:ring-red-200 dark:focus:ring-red-900 transition-all duration-150 shadow-sm hover:border-red-500"
            />
          </div>
        </div>
        <div className="flex gap-2">
          <button
            onClick={handleSearchSubmit}
            disabled={loading}
            className="bg-blue-500 text-white px-6 py-2 rounded-md font-semibold hover:bg-blue-600 focus:ring-2 focus:ring-blue-300 dark:focus:ring-blue-700 transition-all duration-150 shadow-md disabled:opacity-50"
          >
            {loading ? "Searching..." : "Search"}
          </button>
          <button
            onClick={handleClearFilters}
            disabled={loading}
            className="bg-gray-500 text-white px-6 py-2 rounded-md font-semibold hover:bg-gray-600 focus:ring-2 focus:ring-gray-300 dark:focus:ring-gray-700 transition-all duration-150 shadow-md disabled:opacity-50"
          >
            Clear
          </button>
        </div>
      </div>

      {/* Results Summary */}
      <div className="mb-4">
        <p className="text-sm text-gray-600 dark:text-gray-400">
          Showing {userlist.length} of {totalCount.toLocaleString()} users
          {totalPages > 1 && ` (Page ${currentPage} of ${totalPages})`}
        </p>
      </div>

      {/* Users Table */}
      <div className="rounded-2xl border border-gray-200 dark:border-gray-700 bg-white dark:bg-gray-800 px-5 pt-6 pb-2.5 shadow-default sm:px-7.5 xl:pb-1 transition-colors duration-300">
        <div className="py-6">
          {loading ? (
            <div className="flex justify-center items-center py-8">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
              <span className="ml-2 text-gray-600 dark:text-gray-300">
                Loading...
              </span>
            </div>
          ) : userlist.length === 0 ? (
            <div className="text-center py-8">
              <p className="text-gray-500 dark:text-gray-400">No users found</p>
            </div>
          ) : (
            <div className="overflow-hidden rounded-lg shadow">
              <div className="overflow-x-auto">
                <table className="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
                  <thead className="bg-gray-50 dark:bg-gray-700">
                    <tr>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                        User ID
                      </th>
                      <th className="px-6 py-3 text-center text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                        Profile
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                        Email
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                        Status
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                        Action
                      </th>
                    </tr>
                  </thead>
                  <tbody className="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700 ">
                    {userlist.map((user, index) => (
                      <tr
                        key={user.id}
                        className="hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors duration-200"
                      >
                        <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-600 dark:text-gray-300">
                          {user.id}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <div className="flex items-center justify-center">
                            {user.profile_picture ? (
                              <img
                                src={
                                  user.profile_picture
                                    ? `https://room8.flexioninfotech.com${user.profile_picture}`
                                    : "/default-avatar.png"
                                }
                                alt={user.username}
                                className="h-12 w-12 rounded-full border-2 border-gray-300 dark:border-gray-500 object-cover"
                              />
                            ) : (
                              <div className="h-12 w-12 rounded-full bg-gray-200 dark:bg-gray-600 border-2 border-gray-300 dark:border-gray-500 text-gray-600 dark:text-gray-300 flex items-center justify-center">
                                <span className="text-red-500 dark:text-red-400 font-semibold">
                                  {user.username?.charAt(0).toUpperCase() ||
                                    "U"}
                                </span>
                              </div>
                            )}
                          </div>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-600 dark:text-gray-300">
                          {user.email}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          {user.is_deleted ? (
                            <span className="inline-flex items-center px-3 py-1 text-xs font-semibold rounded-full bg-red-100 dark:bg-red-900/30 text-red-800 dark:text-red-200 border border-red-200 dark:border-red-800">
                              <svg
                                className="w-3 h-3 mr-1"
                                fill="currentColor"
                                viewBox="0 0 20 20"
                              >
                                <path
                                  fillRule="evenodd"
                                  d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z"
                                  clipRule="evenodd"
                                />
                              </svg>
                              Deleted
                            </span>
                          ) : (
                            <span className="inline-flex items-center px-3 py-1 text-xs font-semibold rounded-full bg-green-100 dark:bg-green-900/30 text-green-800 dark:text-green-200 border border-green-200 dark:border-green-800">
                              <svg
                                className="w-3 h-3 mr-1"
                                fill="currentColor"
                                viewBox="0 0 20 20"
                              >
                                <path
                                  fillRule="evenodd"
                                  d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z"
                                  clipRule="evenodd"
                                />
                              </svg>
                              Active
                            </span>
                          )}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm font-medium flex gap-2 items-center">
                          {!user.is_deleted && (
                            <>
                              {/* View Button */}
                              {viewLoadingId === user.id ? (
                                <div className="flex justify-center items-center py-2">
                                  <div className="animate-spin rounded-full h-7 w-7 border-b-2 border-blue-500"></div>
                                </div>
                              ) : (
                                // View Button
                                <button
                                  onClick={() => handleView(user)}
                                  className="text-blue-600 dark:text-blue-400 hover:text-blue-900 dark:hover:text-blue-300 p-2 rounded-md hover:bg-blue-50 dark:hover:bg-blue-900/20 transition-colors"
                                >
                                  <svg
                                    className="w-5 h-5"
                                    fill="none"
                                    stroke="currentColor"
                                    viewBox="0 0 24 24"
                                  >
                                    <path
                                      strokeLinecap="round"
                                      strokeLinejoin="round"
                                      strokeWidth="2"
                                      d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"
                                    />
                                    <path
                                      strokeLinecap="round"
                                      strokeLinejoin="round"
                                      strokeWidth="2"
                                      d="M2.458 12C3.732 7.943 7.523 5 12 5c4.477 0 8.268 2.943 9.542 7-1.274 4.057-5.065 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"
                                    />
                                  </svg>
                                </button>
                              )}

                              {/* Delete Button */}
                              <button
                                onClick={() => handleClickOpen(user)}
                                className="text-red-600 dark:text-red-400 hover:text-red-900 dark:hover:text-red-300 p-2 rounded-md hover:bg-red-50 dark:hover:bg-red-900/20 transition-colors"
                              >
                                <svg
                                  className="w-5 h-5"
                                  fill="none"
                                  stroke="currentColor"
                                  viewBox="0 0 24 24"
                                >
                                  <path
                                    strokeLinecap="round"
                                    strokeLinejoin="round"
                                    strokeWidth="2"
                                    d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"
                                  ></path>
                                </svg>
                              </button>
                            </>
                          )}
                        </td>
                        {isViewModalOpen && viewUser && (
                          <UserView
                            viewUser={viewUser}
                            isViewModalOpen={isViewModalOpen}
                            handleCloseViewModal={handleCloseViewModal}
                          />
                        )}
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            </div>
          )}

          {!loading && userlist.length > 0 && (
            <Pagination
              totalPages={totalPages}
              currentPage={currentPage}
              handlePrevious={handlePrevious}
              handleNext={handleNext}
              handlePageClick={handlePageClick}
              totalCount={totalCount}
              itemsPerPage={itemsPerPage}
            />
          )}
        </div>
      </div>

      <DeleteModal
        isOpen={deleteOpen}
        onClose={handleDialogClose}
        user={selectedUser}
        onConfirm={handleConfirmDelete}
        loading={deleteLoading}
      />
    </div>
  );
};

export default UserManagement;
